version: '3.8'

# Production overrides for MVS-VR v2
services:
  app:
    restart: always
    environment:
      - NODE_ENV=production
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  directus:
    restart: always
    environment:
      - NODE_ENV=production
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  postgres:
    restart: always
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD:-9elskdUeo@I!}
      - POSTGRES_USER=${DB_USER:-mvsadmin}
      - POSTGRES_DB=${DB_NAME:-mvs_vr}
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups

  redis:
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  elasticsearch:
    restart: always
    environment:
      - 'ES_JAVA_OPTS=-Xms1g -Xmx1g'
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  kibana:
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  prometheus:
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  grafana:
    restart: always
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-mvsadmin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-9elskdUeo@I!}
      - GF_SECURITY_SECRET_KEY=${GRAFANA_SECRET_KEY:-7f8a9b2c4d5e6f1a8b9c0d1e2f3a4b5c}
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  minio:
    restart: always
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-mvsadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-9elskdUeo@I!}
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  rabbitmq:
    restart: always
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER:-mvsadmin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS:-9elskdUeo@I!}
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  nginx:
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  # Add backup service for production
  backup:
    image: postgres:15-alpine
    restart: 'no'
    environment:
      - PGPASSWORD=${DB_PASSWORD:-9elskdUeo@I!}
    volumes:
      - postgres_data:/var/lib/postgresql/data:ro
      - ./backups:/backups
    command: |
      sh -c '
        while true; do
          pg_dump -h postgres -U ${DB_USER:-mvsadmin} -d ${DB_NAME:-mvs_vr} > /backups/backup_$$(date +%Y%m%d_%H%M%S).sql
          find /backups -name "backup_*.sql" -mtime +30 -delete
          sleep 86400
        done
      '
    depends_on:
      - postgres
    networks:
      - mvs-network

  # Add log aggregation
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    user: root
    restart: always
    volumes:
      - ./deployment/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - mvs-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  directus_uploads:
    driver: local
  app_uploads:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  minio_data:
    driver: local
  rabbitmq_data:
    driver: local
