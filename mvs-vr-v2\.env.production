# MVS-VR v2 Production Environment Configuration

# Application
NODE_ENV=production
PORT=3000
APP_URL=https://your-domain.com
API_URL=https://your-domain.com/api

# Database
DATABASE_URL=********************************************************/mvs_vr
DB_HOST=postgres
DB_PORT=5432
DB_NAME=mvs_vr
DB_USER=postgres
DB_PASSWORD=your-secure-password

# Redis
REDIS_URL=redis://redis:6379
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Directus
DIRECTUS_URL=http://directus:8055
DIRECTUS_KEY=your-directus-key-32-chars-long
DIRECTUS_SECRET=your-directus-secret-32-chars-long
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=your-secure-admin-password

# Authentication
JWT_SECRET=your-jwt-secret-key-64-chars-long
SESSION_SECRET=your-session-secret-key-64-chars-long
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# External Services
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Email (for notifications)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password
SMTP_FROM=<EMAIL>

# Object Storage (MinIO)
MINIO_ENDPOINT=http://minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=your-secure-minio-password
MINIO_BUCKET=mvs-vr-assets

# Message Queue (RabbitMQ)
RABBITMQ_URL=amqp://admin:your-secure-rabbitmq-password@rabbitmq:5672
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=your-secure-rabbitmq-password

# Elasticsearch
ELASTICSEARCH_URL=http://elasticsearch:9200
ELASTICSEARCH_INDEX=mvs-vr-logs

# Monitoring
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000
GRAFANA_ADMIN_PASSWORD=your-secure-grafana-password

# Security
CORS_ORIGIN=https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Features
MONITORING_ENABLED=true
ANALYTICS_ENABLED=true
ML_ENABLED=true
CACHING_ENABLED=true
COMPRESSION_ENABLED=true

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/app/logs/application.log

# Performance
MAX_FILE_SIZE=100mb
MAX_REQUEST_SIZE=50mb
WORKER_PROCESSES=auto
KEEP_ALIVE_TIMEOUT=65

# SSL/TLS (when using HTTPS)
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/certs/key.pem

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Health Checks
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Development/Debug (set to false in production)
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_PROFILING=false
