# MVS-VR v2 Production Environment Configuration

# Application
NODE_ENV=production
PORT=3000
APP_URL=https://your-domain.com
API_URL=https://your-domain.com/api

# Database
DATABASE_URL=************************************************/mvs_vr
DB_HOST=postgres
DB_PORT=5432
DB_NAME=mvs_vr
DB_USER=mvsadmin
DB_PASSWORD=9elskdUeo@I!

# Redis
REDIS_URL=redis://:9elskdUeo@I!@redis:6379
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=9elskdUeo@I!

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Directus
DIRECTUS_URL=http://directus:8055
DIRECTUS_KEY=7f8a9b2c4d5e6f1a8b9c0d1e2f3a4b5c
DIRECTUS_SECRET=9c8b7a6f5e4d3c2b1a0f9e8d7c6b5a4f
DIRECTUS_DB=directus
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=9elskdUeo@I!

# Authentication
JWT_SECRET=3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c5b4a3f2e
SESSION_SECRET=8e7d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d
ENCRYPTION_KEY=5a4b3c2d1e0f9a8b7c6d5e4f3a2b1c0d

# External Services
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Email (for notifications)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password
SMTP_FROM=<EMAIL>

# Object Storage (MinIO)
MINIO_ENDPOINT=http://minio:9000
MINIO_ROOT_USER=mvsadmin
MINIO_ROOT_PASSWORD=9elskdUeo@I!
MINIO_ACCESS_KEY=mvsadmin
MINIO_SECRET_KEY=9elskdUeo@I!
MINIO_BUCKET=mvs-vr-assets

# Message Queue (RabbitMQ)
RABBITMQ_URL=amqp://mvsadmin:9elskdUeo@I!@rabbitmq:5672
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_DEFAULT_USER=mvsadmin
RABBITMQ_DEFAULT_PASS=9elskdUeo@I!

# Elasticsearch
ELASTICSEARCH_URL=http://elasticsearch:9200
ELASTICSEARCH_INDEX=mvs-vr-logs

# Monitoring
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000
GRAFANA_ADMIN_USER=mvsadmin
GRAFANA_ADMIN_PASSWORD=9elskdUeo@I!
GRAFANA_SECRET_KEY=7f8a9b2c4d5e6f1a8b9c0d1e2f3a4b5c

# Security
CORS_ORIGIN=https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Features
MONITORING_ENABLED=true
ANALYTICS_ENABLED=true
ML_ENABLED=true
CACHING_ENABLED=true
COMPRESSION_ENABLED=true

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/app/logs/application.log

# Performance
MAX_FILE_SIZE=100mb
MAX_REQUEST_SIZE=50mb
WORKER_PROCESSES=auto
KEEP_ALIVE_TIMEOUT=65

# SSL/TLS (when using HTTPS)
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/certs/key.pem

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Health Checks
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Development/Debug (set to false in production)
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_PROFILING=false
