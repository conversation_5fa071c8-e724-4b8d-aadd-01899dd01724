#!/bin/bash

# MVS-VR v2 Deployment Script
# This script deploys the complete MVS-VR v2 stack to DigitalOcean

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DROPLET_IP="${DROPLET_IP:-your-droplet-ip}"
DROPLET_USER="${DROPLET_USER:-root}"
PROJECT_NAME="mvs-vr-v2"
REMOTE_PATH="/opt/${PROJECT_NAME}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check if Docker is installed locally
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not available. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if rsync is available
    if ! command -v rsync &> /dev/null; then
        log_error "rsync is not installed. Please install rsync first."
        exit 1
    fi
    
    log_success "All requirements met"
}

prepare_deployment() {
    log_info "Preparing deployment package..."
    
    # Create deployment directory
    mkdir -p ./deployment-package
    
    # Copy necessary files
    cp -r ./implementation/server ./deployment-package/
    cp -r ./deployment ./deployment-package/
    cp ./docker-compose.yml ./deployment-package/
    cp ./.env.staging.example ./deployment-package/.env
    
    # Remove unnecessary files
    find ./deployment-package/server -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
    find ./deployment-package/server -name ".next" -type d -exec rm -rf {} + 2>/dev/null || true
    find ./deployment-package/server -name "coverage" -type d -exec rm -rf {} + 2>/dev/null || true
    find ./deployment-package/server -name "*.log" -type f -delete 2>/dev/null || true
    
    log_success "Deployment package prepared"
}

upload_files() {
    log_info "Uploading files to DigitalOcean droplet..."
    
    # Create remote directory
    ssh ${DROPLET_USER}@${DROPLET_IP} "mkdir -p ${REMOTE_PATH}"
    
    # Upload deployment package
    rsync -avz --progress ./deployment-package/ ${DROPLET_USER}@${DROPLET_IP}:${REMOTE_PATH}/
    
    log_success "Files uploaded successfully"
}

setup_environment() {
    log_info "Setting up environment on droplet..."
    
    ssh ${DROPLET_USER}@${DROPLET_IP} << EOF
        # Update system
        apt-get update
        apt-get upgrade -y
        
        # Install Docker if not present
        if ! command -v docker &> /dev/null; then
            curl -fsSL https://get.docker.com -o get-docker.sh
            sh get-docker.sh
            systemctl enable docker
            systemctl start docker
        fi
        
        # Install Docker Compose if not present
        if ! command -v docker-compose &> /dev/null; then
            curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)" -o /usr/local/bin/docker-compose
            chmod +x /usr/local/bin/docker-compose
        fi
        
        # Create necessary directories
        mkdir -p ${REMOTE_PATH}/data
        mkdir -p ${REMOTE_PATH}/logs
        mkdir -p ${REMOTE_PATH}/ssl
        
        # Set permissions
        chown -R 1001:1001 ${REMOTE_PATH}/data
        chown -R 1001:1001 ${REMOTE_PATH}/logs
        
        cd ${REMOTE_PATH}
        
        # Generate random secrets for Directus
        sed -i "s/replace-with-random-value-32-chars-long/\$(openssl rand -hex 16)/g" .env
        
        log_success "Environment setup completed"
EOF
}

deploy_services() {
    log_info "Deploying services..."
    
    ssh ${DROPLET_USER}@${DROPLET_IP} << EOF
        cd ${REMOTE_PATH}
        
        # Pull latest images
        docker-compose pull
        
        # Build custom images
        docker-compose build
        
        # Start services
        docker-compose up -d
        
        # Wait for services to be ready
        echo "Waiting for services to start..."
        sleep 30
        
        # Check service health
        docker-compose ps
EOF
    
    log_success "Services deployed successfully"
}

verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check if services are responding
    if curl -f http://${DROPLET_IP}/health &> /dev/null; then
        log_success "Main application is responding"
    else
        log_warning "Main application health check failed"
    fi
    
    if curl -f http://${DROPLET_IP}/admin/server/health &> /dev/null; then
        log_success "Directus is responding"
    else
        log_warning "Directus health check failed"
    fi
    
    log_info "Deployment verification completed"
}

cleanup() {
    log_info "Cleaning up..."
    rm -rf ./deployment-package
    log_success "Cleanup completed"
}

show_info() {
    log_info "Deployment completed! Access your services at:"
    echo ""
    echo "🌐 Main Application: http://${DROPLET_IP}"
    echo "⚙️  Admin Panel (Directus): http://${DROPLET_IP}/admin"
    echo "📊 Monitoring (Grafana): http://${DROPLET_IP}/monitoring"
    echo "📈 Analytics (Kibana): http://${DROPLET_IP}/analytics"
    echo "🗄️  Object Storage (MinIO): http://${DROPLET_IP}:9001"
    echo "🐰 Message Queue (RabbitMQ): http://${DROPLET_IP}:15672"
    echo ""
    echo "Default credentials:"
    echo "- Directus: <EMAIL> / admin123"
    echo "- Grafana: admin / admin123"
    echo "- MinIO: minioadmin / minioadmin123"
    echo "- RabbitMQ: admin / admin123"
    echo ""
    echo "⚠️  Please change default passwords in production!"
}

# Main execution
main() {
    log_info "Starting MVS-VR v2 deployment..."
    
    check_requirements
    prepare_deployment
    upload_files
    setup_environment
    deploy_services
    verify_deployment
    cleanup
    show_info
    
    log_success "Deployment completed successfully!"
}

# Check if DROPLET_IP is set
if [ "$DROPLET_IP" = "your-droplet-ip" ]; then
    log_error "Please set DROPLET_IP environment variable"
    echo "Usage: DROPLET_IP=your.server.ip ./deploy.sh"
    exit 1
fi

# Run main function
main "$@"
