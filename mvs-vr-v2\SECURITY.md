# MVS-VR v2 Security Configuration

## 🔐 Secure Credentials

All services in the MVS-VR v2 stack have been configured with secure, consistent credentials to maintain security across the deployment.

### Default Secure Credentials

**Username**: `mvsadmin`  
**Password**: `9elskdUeo@I!`

These credentials are used across all services:
- PostgreSQL Database
- Redis Cache
- Directus CMS
- Grafana Monitoring
- MinIO Object Storage
- RabbitMQ Message Queue

### Security Features Implemented

#### 🛡️ Authentication & Authorization
- **Strong passwords** with special characters, numbers, and mixed case
- **Consistent user management** across all services
- **JWT-based authentication** with secure secret keys
- **Session management** with encrypted session storage
- **Role-based access control** in Directus

#### 🔒 Encryption & Secrets
- **32-character encryption keys** for sensitive data
- **64-character JWT secrets** for token security
- **Random secret generation** for each deployment
- **Environment variable protection** for sensitive configuration

#### 🌐 Network Security
- **Redis password protection** with requirepass directive
- **Database authentication** with secure user credentials
- **CORS configuration** to prevent unauthorized access
- **Rate limiting** to prevent abuse and DDoS attacks

#### 📊 Monitoring & Logging
- **Secure Grafana access** with admin credentials
- **Protected monitoring endpoints** with authentication
- **Audit logging** for security events
- **Health check endpoints** with access controls

## 🔧 Configuration Details

### Database Security (PostgreSQL)
```env
DB_USER=mvsadmin
DB_PASSWORD=9elskdUeo@I!
DATABASE_URL=************************************************/mvs_vr
```

### Cache Security (Redis)
```env
REDIS_PASSWORD=9elskdUeo@I!
REDIS_URL=redis://:9elskdUeo@I!@redis:6379
```

### CMS Security (Directus)
```env
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=9elskdUeo@I!
DIRECTUS_KEY=7f8a9b2c4d5e6f1a8b9c0d1e2f3a4b5c
DIRECTUS_SECRET=9c8b7a6f5e4d3c2b1a0f9e8d7c6b5a4f
```

### Monitoring Security (Grafana)
```env
GRAFANA_ADMIN_USER=mvsadmin
GRAFANA_ADMIN_PASSWORD=9elskdUeo@I!
GRAFANA_SECRET_KEY=7f8a9b2c4d5e6f1a8b9c0d1e2f3a4b5c
```

### Object Storage Security (MinIO)
```env
MINIO_ROOT_USER=mvsadmin
MINIO_ROOT_PASSWORD=9elskdUeo@I!
```

### Message Queue Security (RabbitMQ)
```env
RABBITMQ_DEFAULT_USER=mvsadmin
RABBITMQ_DEFAULT_PASS=9elskdUeo@I!
```

## 🚀 Deployment Security

### Environment Files
- **`.env.secure`** - Contains all secure default credentials
- **`.env.production`** - Production-ready configuration
- **Environment variable fallbacks** in Docker Compose

### Docker Security
- **Non-root users** in all containers
- **Health checks** for service monitoring
- **Resource limits** to prevent resource exhaustion
- **Network isolation** with custom Docker networks

### SSL/HTTPS Configuration
```nginx
# SSL configuration in Nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
ssl_prefer_server_ciphers off;
```

## 🔄 Security Best Practices

### 1. Credential Management
- **Use environment variables** for all sensitive data
- **Rotate credentials regularly** (recommended: every 90 days)
- **Use different credentials** for different environments
- **Store secrets securely** using tools like HashiCorp Vault

### 2. Network Security
- **Enable firewall** on your server
- **Use SSL/HTTPS** for all external communications
- **Implement fail2ban** to prevent brute force attacks
- **Regular security updates** for all system packages

### 3. Monitoring & Alerting
- **Monitor failed login attempts** across all services
- **Set up alerts** for unusual activity
- **Regular security audits** of access logs
- **Automated backup verification**

### 4. Access Control
- **Principle of least privilege** for all users
- **Regular access reviews** and cleanup
- **Multi-factor authentication** where possible
- **VPN access** for administrative tasks

## 🛠️ Security Maintenance

### Regular Tasks
- [ ] **Weekly**: Review access logs for anomalies
- [ ] **Monthly**: Update system packages and security patches
- [ ] **Quarterly**: Rotate service credentials
- [ ] **Annually**: Full security audit and penetration testing

### Security Checklist
- [ ] All default passwords changed
- [ ] SSL/HTTPS configured and working
- [ ] Firewall rules properly configured
- [ ] Backup encryption enabled
- [ ] Monitoring alerts configured
- [ ] Access logs being collected
- [ ] Security updates automated

## 🚨 Incident Response

### In Case of Security Breach
1. **Immediately change all credentials**
2. **Review access logs** for unauthorized activity
3. **Check data integrity** and restore from backups if needed
4. **Update security measures** to prevent future incidents
5. **Document the incident** for future reference

### Emergency Contacts
- System Administrator: [Your contact info]
- Security Team: [Your security team contact]
- Hosting Provider: DigitalOcean Support

## 📞 Support & Resources

### Security Resources
- [OWASP Security Guidelines](https://owasp.org/)
- [Docker Security Best Practices](https://docs.docker.com/engine/security/)
- [PostgreSQL Security](https://www.postgresql.org/docs/current/security.html)
- [Redis Security](https://redis.io/topics/security)

### Getting Help
For security-related questions or concerns:
1. Check this documentation first
2. Review service-specific security documentation
3. Contact your system administrator
4. Consult with security professionals if needed

---

**Remember**: Security is an ongoing process, not a one-time setup. Regular maintenance and monitoring are essential for maintaining a secure deployment.
