version: '3.8'

services:
  # Main API Server
  api:
    build:
      context: ./implementation/server
      dockerfile: Dockerfile
      target: production
    container_name: mvs-api-staging
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=staging
      - PORT=3000
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/mvs_staging
      - REDIS_URL=redis://redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - DIRECTUS_URL=http://directus:8055
      - JWT_SECRET=${JWT_SECRET}
      - CSRF_SECRET=${CSRF_SECRET}
      - API_KEY=${API_KEY}
      - LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mvs-network

  # Directus CMS
  directus:
    image: directus/directus:10.8.3
    container_name: mvs-directus-staging
    ports:
      - "8055:8055"
    environment:
      KEY: ${DIRECTUS_KEY}
      SECRET: ${DIRECTUS_SECRET}
      DB_CLIENT: pg
      DB_HOST: postgres
      DB_PORT: 5432
      DB_DATABASE: directus
      DB_USER: directus
      DB_PASSWORD: ${DIRECTUS_DB_PASSWORD}
      CACHE_ENABLED: true
      CACHE_STORE: redis
      REDIS: redis://redis:6379
      ADMIN_EMAIL: ${DIRECTUS_ADMIN_EMAIL}
      ADMIN_PASSWORD: ${DIRECTUS_ADMIN_PASSWORD}
      PUBLIC_URL: https://admin.mvs.kanousai.com
      CORS_ENABLED: true
      CORS_ORIGIN: true
      RATE_LIMITER_ENABLED: true
      RATE_LIMITER_POINTS: 100
      RATE_LIMITER_DURATION: 60
      LOG_LEVEL: info
      EMAIL_FROM: <EMAIL>
      EMAIL_TRANSPORT: smtp
      EMAIL_SMTP_HOST: ${SMTP_HOST}
      EMAIL_SMTP_PORT: ${SMTP_PORT}
      EMAIL_SMTP_USER: ${SMTP_USER}
      EMAIL_SMTP_PASSWORD: ${SMTP_PASSWORD}
    volumes:
      - directus_uploads:/directus/uploads
      - directus_database:/directus/database
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8055/server/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - mvs-network

  # PostgreSQL Database
  postgres:
    image: postgres:15.4-alpine
    container_name: mvs-postgres-staging
    environment:
      POSTGRES_DB: mvs_staging
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=en_US.UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deployment/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d mvs_staging"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - mvs-network
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: mvs-redis-staging
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./deployment/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    networks:
      - mvs-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:1.25-alpine
    container_name: mvs-nginx-staging
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/staging.conf:/etc/nginx/conf.d/default.conf
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - api
      - directus
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - mvs-network

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: mvs-prometheus-staging
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - mvs-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:10.1.0
    container_name: mvs-grafana-staging
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - mvs-network

  # Log Management - Loki
  loki:
    image: grafana/loki:2.9.0
    container_name: mvs-loki-staging
    ports:
      - "3100:3100"
    volumes:
      - ./deployment/monitoring/loki.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    restart: unless-stopped
    networks:
      - mvs-network

  # Log Collector - Promtail
  promtail:
    image: grafana/promtail:2.9.0
    container_name: mvs-promtail-staging
    volumes:
      - ./deployment/monitoring/promtail.yml:/etc/promtail/config.yml
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    restart: unless-stopped
    networks:
      - mvs-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  directus_uploads:
    driver: local
  directus_database:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  mvs-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
