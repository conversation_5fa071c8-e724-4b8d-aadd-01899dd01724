FROM directus/directus:10.8.3

# Install dependencies for building extensions and runtime
RUN apk add --no-cache git curl dumb-init

# Set working directory
WORKDIR /directus

# Copy extensions
COPY ./directus/extensions /directus/extensions

# Build extensions
RUN cd /directus/extensions/hooks/supabase-sync && npm install && npm run build
RUN cd /directus/extensions/hooks/role-sync && npm install && npm run build
RUN cd /directus/extensions/hooks/performance-optimizer && npm install && npm run build
RUN cd /directus/extensions/endpoints/api-integration && npm install && npm run build
RUN cd /directus/extensions/endpoints/auth-integration && npm install && npm run build
RUN cd /directus/extensions/interfaces/vendor-portal && npm install && npm run build

# Copy snapshots
COPY ./directus/snapshots /directus/snapshots

# Copy config
COPY ./directus/config /directus/config

# Copy setup script
COPY ./scripts/setup_directus.js /directus/scripts/setup_directus.js

# Install script dependencies
RUN npm install axios dotenv

# Set up entrypoint script
COPY ./scripts/directus-entrypoint.sh /directus/directus-entrypoint.sh
RUN chmod +x /directus/directus-entrypoint.sh

# Create necessary directories
RUN mkdir -p /directus/uploads /directus/database /directus/logs

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8055/server/health || exit 1

# Expose port
EXPOSE 8055

# Use dumb-init to handle signals properly and set the entrypoint
ENTRYPOINT ["dumb-init", "--", "/directus/directus-entrypoint.sh"]
