# DigitalOcean Staging Server Deployment Plan

## Overview
Deploy MVS-VR staging server to DigitalOcean with full integration testing and production-ready configuration.

## Infrastructure Requirements

### DigitalOcean Droplet Specifications
- **Size**: 4 vCPUs, 8GB RAM, 160GB SSD (Production-4GB)
- **Region**: NYC1 (or closest to primary users)
- **OS**: Ubuntu 22.04 LTS
- **Networking**: Private networking enabled
- **Monitoring**: DigitalOcean monitoring enabled
- **Backups**: Weekly automated backups

### Domain Configuration
- **Primary Domain**: `mvs.kanousai.com`
- **Staging Subdomain**: `staging.mvs.kanousai.com`
- **Admin Subdomain**: `admin.mvs.kanousai.com`
- **API Subdomain**: `api.mvs.kanousai.com`

## Deployment Architecture

### Services Stack
```
┌─────────────────────────────────────────┐
│              Load Balancer              │
│            (Nginx Proxy)                │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│              Docker Host                │
│         (DigitalOcean Droplet)          │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   Directus  │  │   Node.js API   │   │
│  │   (CMS)     │  │   (Backend)     │   │
│  │   :8055     │  │     :3000       │   │
│  └─────────────┘  └─────────────────┘   │
│                                         │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │    Redis    │  │   PostgreSQL    │   │
│  │  (Cache)    │  │  (Database)     │   │
│  │   :6379     │  │     :5432       │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│            External Services            │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  Supabase   │  │   File Storage  │   │
│  │ (External)  │  │ (DigitalOcean)  │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

## Deployment Steps

### Phase 1: Server Provisioning
1. **Create DigitalOcean Droplet**
   ```bash
   # Using DigitalOcean CLI
   doctl compute droplet create mvs-staging \
     --size s-4vcpu-8gb \
     --image ubuntu-22-04-x64 \
     --region nyc1 \
     --enable-private-networking \
     --enable-monitoring \
     --enable-backups \
     --ssh-keys [your-ssh-key-id]
   ```

2. **Initial Server Setup**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   
   # Install additional tools
   sudo apt install -y git nginx certbot python3-certbot-nginx
   ```

### Phase 2: Application Deployment
1. **Clone Repository**
   ```bash
   git clone https://github.com/your-org/MVS-VR.git
   cd MVS-VR/mvs-vr-v2
   ```

2. **Environment Configuration**
   ```bash
   # Copy staging environment file
   cp .env.staging .env
   
   # Update environment variables
   nano .env
   ```

3. **Deploy Services**
   ```bash
   # Build and start services
   docker-compose -f docker-compose.staging.yml up -d
   
   # Verify services
   docker-compose ps
   ```

### Phase 3: SSL and Domain Setup
1. **Configure Nginx**
   ```bash
   # Copy nginx configuration
   sudo cp deployment/nginx/staging.conf /etc/nginx/sites-available/mvs-staging
   sudo ln -s /etc/nginx/sites-available/mvs-staging /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

2. **SSL Certificates**
   ```bash
   # Obtain SSL certificates
   sudo certbot --nginx -d staging.mvs.kanousai.com
   sudo certbot --nginx -d admin.mvs.kanousai.com
   sudo certbot --nginx -d api.mvs.kanousai.com
   ```

## Environment Variables

### Required Environment Variables
```bash
# Application
NODE_ENV=staging
PORT=3000
API_BASE_URL=https://api.mvs.kanousai.com

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/mvs_staging
REDIS_URL=redis://localhost:6379

# Supabase (Staging)
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Directus
DIRECTUS_URL=https://admin.mvs.kanousai.com
DIRECTUS_EMAIL=<EMAIL>
DIRECTUS_PASSWORD=[secure-password]

# Security
JWT_SECRET=[secure-jwt-secret]
CSRF_SECRET=[secure-csrf-secret]
API_KEY=[secure-api-key]

# Monitoring
SENTRY_DSN=[sentry-dsn]
LOG_LEVEL=info
```

## Docker Compose Configuration

### staging docker-compose.yml
```yaml
version: '3.8'

services:
  api:
    build: 
      context: ./implementation/server
      dockerfile: Dockerfile.staging
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=staging
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  directus:
    image: directus/directus:latest
    ports:
      - "8055:8055"
    environment:
      - DB_CLIENT=pg
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_DATABASE=directus
      - DB_USER=directus
      - DB_PASSWORD=[secure-password]
      - KEY=[secure-key]
      - SECRET=[secure-secret]
    depends_on:
      - postgres
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=mvs_staging
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=[secure-password]
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx:/etc/nginx/conf.d
      - /etc/letsencrypt:/etc/letsencrypt
    depends_on:
      - api
      - directus
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## Post-Deployment Validation

### Health Checks
```bash
# API Health Check
curl https://api.mvs.kanousai.com/health

# Directus Health Check
curl https://admin.mvs.kanousai.com/server/health

# Database Connection
docker exec mvs-postgres pg_isready

# Redis Connection
docker exec mvs-redis redis-cli ping
```

### Smoke Tests
```bash
# Run staging smoke tests
npm run test:staging

# Validate integrations
npm run test:staging:validate
```

## Monitoring and Logging

### Log Aggregation
- **Application Logs**: `/var/log/mvs/`
- **Nginx Logs**: `/var/log/nginx/`
- **Docker Logs**: `docker-compose logs -f`

### Monitoring Setup
- **DigitalOcean Monitoring**: Enabled
- **Application Metrics**: Prometheus + Grafana
- **Error Tracking**: Sentry integration
- **Uptime Monitoring**: External service

## Security Configuration

### Firewall Rules
```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### Security Headers
- HTTPS enforcement
- CSRF protection
- Rate limiting
- API key authentication
- Security headers (HSTS, CSP, etc.)

## Backup Strategy

### Automated Backups
- **Database**: Daily PostgreSQL dumps
- **Files**: Weekly file system backups
- **DigitalOcean**: Weekly droplet snapshots
- **Retention**: 30 days

### Backup Commands
```bash
# Database backup
docker exec mvs-postgres pg_dump -U postgres mvs_staging > backup_$(date +%Y%m%d).sql

# File backup
tar -czf files_backup_$(date +%Y%m%d).tar.gz /app/uploads
```

## Rollback Plan

### Quick Rollback
```bash
# Stop current deployment
docker-compose down

# Restore from backup
docker-compose -f docker-compose.backup.yml up -d

# Verify services
./scripts/health-check.sh
```

## Success Criteria

### Deployment Success
- [ ] All services running and healthy
- [ ] SSL certificates installed and valid
- [ ] Domain routing configured correctly
- [ ] Database migrations completed
- [ ] Integration tests passing
- [ ] Monitoring and logging operational

### Performance Targets
- [ ] API response time < 200ms
- [ ] Page load time < 2 seconds
- [ ] 99.9% uptime target
- [ ] Database query time < 100ms

---
**Status**: Ready for deployment
**Estimated Deployment Time**: 2-3 hours
**Rollback Time**: 15 minutes
