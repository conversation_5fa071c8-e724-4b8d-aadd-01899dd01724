#!/bin/bash

# MVS-VR Staging Deployment Script
# This script deploys the MVS-VR application to DigitalOcean staging environment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="staging"
DOCKER_COMPOSE_FILE="docker-compose.staging.yml"
ENV_FILE=".env.staging"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if environment file exists
    if [[ ! -f "$PROJECT_DIR/$ENV_FILE" ]]; then
        log_error "Environment file $ENV_FILE not found. Please copy from .env.staging.example and configure."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

backup_existing_deployment() {
    log_info "Creating backup of existing deployment..."
    
    if docker-compose -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" ps -q | grep -q .; then
        # Create backup directory
        BACKUP_DIR="$PROJECT_DIR/backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # Backup database
        log_info "Backing up database..."
        docker-compose -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump -U postgres mvs_staging > "$BACKUP_DIR/database_backup.sql" || true
        
        # Backup volumes
        log_info "Backing up volumes..."
        docker run --rm -v mvs-vr-v2_postgres_data:/data -v "$BACKUP_DIR":/backup alpine tar czf /backup/postgres_data.tar.gz -C /data . || true
        docker run --rm -v mvs-vr-v2_directus_uploads:/data -v "$BACKUP_DIR":/backup alpine tar czf /backup/directus_uploads.tar.gz -C /data . || true
        
        log_success "Backup created at $BACKUP_DIR"
    else
        log_info "No existing deployment found, skipping backup"
    fi
}

run_tests() {
    log_info "Running pre-deployment tests..."
    
    cd "$PROJECT_DIR/implementation/server"
    
    # Run stable tests
    if npm run test:stable; then
        log_success "Stable tests passed"
    else
        log_error "Stable tests failed. Deployment aborted."
        exit 1
    fi
    
    # Run comprehensive integration tests
    if npm run test:comprehensive-integration; then
        log_success "Integration tests passed"
    else
        log_warning "Integration tests failed, but continuing with deployment"
    fi
    
    cd "$PROJECT_DIR"
}

build_images() {
    log_info "Building Docker images..."
    
    cd "$PROJECT_DIR"
    
    # Build images
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    log_success "Docker images built successfully"
}

deploy_services() {
    log_info "Deploying services..."
    
    cd "$PROJECT_DIR"
    
    # Stop existing services
    docker-compose -f "$DOCKER_COMPOSE_FILE" down || true
    
    # Start services
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log_success "Services deployed"
}

wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Wait for PostgreSQL
    log_info "Waiting for PostgreSQL..."
    timeout 60 bash -c 'until docker-compose -f '"$PROJECT_DIR/$DOCKER_COMPOSE_FILE"' exec -T postgres pg_isready -U postgres; do sleep 2; done'
    
    # Wait for Redis
    log_info "Waiting for Redis..."
    timeout 60 bash -c 'until docker-compose -f '"$PROJECT_DIR/$DOCKER_COMPOSE_FILE"' exec -T redis redis-cli ping; do sleep 2; done'
    
    # Wait for API
    log_info "Waiting for API..."
    timeout 120 bash -c 'until curl -f http://localhost:3000/health; do sleep 5; done'
    
    # Wait for Directus
    log_info "Waiting for Directus..."
    timeout 120 bash -c 'until curl -f http://localhost:8055/server/health; do sleep 5; done'
    
    log_success "All services are ready"
}

run_migrations() {
    log_info "Running database migrations..."
    
    # Run API migrations
    docker-compose -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" exec -T api npm run migrate || true
    
    # Initialize Directus schema
    docker-compose -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" exec -T directus npx directus schema apply --yes || true
    
    log_success "Migrations completed"
}

setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    # Check if certificates already exist
    if [[ -f "/etc/letsencrypt/live/api.mvs.kanousai.com/fullchain.pem" ]]; then
        log_info "SSL certificates already exist, skipping setup"
        return
    fi
    
    log_warning "SSL certificates not found. Please run the following commands manually:"
    echo "sudo certbot --nginx -d api.mvs.kanousai.com"
    echo "sudo certbot --nginx -d admin.mvs.kanousai.com"
    echo "sudo certbot --nginx -d staging.mvs.kanousai.com"
    
    read -p "Press Enter after setting up SSL certificates..."
}

run_health_checks() {
    log_info "Running health checks..."
    
    # API Health Check
    if curl -f http://localhost:3000/health; then
        log_success "API health check passed"
    else
        log_error "API health check failed"
        return 1
    fi
    
    # Directus Health Check
    if curl -f http://localhost:8055/server/health; then
        log_success "Directus health check passed"
    else
        log_error "Directus health check failed"
        return 1
    fi
    
    # Database Health Check
    if docker-compose -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" exec -T postgres pg_isready -U postgres; then
        log_success "Database health check passed"
    else
        log_error "Database health check failed"
        return 1
    fi
    
    # Redis Health Check
    if docker-compose -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" exec -T redis redis-cli ping; then
        log_success "Redis health check passed"
    else
        log_error "Redis health check failed"
        return 1
    fi
    
    log_success "All health checks passed"
}

setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Create monitoring directories
    mkdir -p "$PROJECT_DIR/deployment/monitoring/grafana/provisioning"
    mkdir -p "$PROJECT_DIR/logs"
    
    # Set permissions
    chmod 755 "$PROJECT_DIR/logs"
    
    log_success "Monitoring setup completed"
}

show_deployment_info() {
    log_success "Deployment completed successfully!"
    echo ""
    echo "=== Deployment Information ==="
    echo "Environment: $DEPLOYMENT_ENV"
    echo "Timestamp: $(date)"
    echo ""
    echo "=== Service URLs ==="
    echo "API: https://api.mvs.kanousai.com"
    echo "Admin: https://admin.mvs.kanousai.com"
    echo "Staging: https://staging.mvs.kanousai.com"
    echo ""
    echo "=== Service Status ==="
    docker-compose -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" ps
    echo ""
    echo "=== Next Steps ==="
    echo "1. Configure DNS records to point to this server"
    echo "2. Set up SSL certificates if not already done"
    echo "3. Run smoke tests against the staging environment"
    echo "4. Configure monitoring alerts"
    echo ""
    echo "=== Useful Commands ==="
    echo "View logs: docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
    echo "Restart services: docker-compose -f $DOCKER_COMPOSE_FILE restart"
    echo "Stop services: docker-compose -f $DOCKER_COMPOSE_FILE down"
    echo "Update services: ./scripts/deploy-staging.sh"
}

cleanup_on_error() {
    log_error "Deployment failed. Cleaning up..."
    docker-compose -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" down || true
    exit 1
}

# Main deployment process
main() {
    log_info "Starting MVS-VR staging deployment..."
    
    # Set error trap
    trap cleanup_on_error ERR
    
    # Run deployment steps
    check_prerequisites
    backup_existing_deployment
    run_tests
    build_images
    setup_monitoring
    deploy_services
    wait_for_services
    run_migrations
    run_health_checks
    show_deployment_info
    
    log_success "Deployment completed successfully!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --skip-tests    Skip running tests before deployment"
            echo "  --skip-backup   Skip creating backup before deployment"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
