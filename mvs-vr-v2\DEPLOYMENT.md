# MVS-VR v2 Deployment Guide

This guide covers deploying the complete MVS-VR v2 stack to DigitalOcean using Docker.

## 🏗️ Architecture Overview

The deployment includes:

- **Main Application** (Node.js/Next.js) - Port 3000
- **Directus CMS** - Port 8055
- **PostgreSQL Database** - Port 5432
- **<PERSON>is <PERSON>ache** - Port 6379
- **Elasticsearch** - Port 9200
- **Kibana Analytics** - Port 5601
- **Prometheus Monitoring** - Port 9090
- **Grafana Dashboard** - Port 3001
- **MinIO Object Storage** - Ports 9000, 9001
- **RabbitMQ Message Queue** - Ports 5672, 15672
- **Nginx Reverse Proxy** - Ports 80, 443

## 🚀 Quick Deployment

### Prerequisites

1. **DigitalOcean Droplet** (minimum 4GB RAM, 2 vCPUs)
2. **Docker** and **Docker Compose** installed locally
3. **SSH access** to your droplet

### One-Command Deployment

```bash
# Set your droplet IP and run the deployment script
DROPLET_IP=your.droplet.ip ./deploy.sh
```

## 📋 Manual Deployment Steps

### 1. Prepare Your Droplet

```bash
# SSH into your droplet
ssh <EMAIL>

# Update system
apt update && apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

### 2. Upload Project Files

```bash
# From your local machine
rsync -avz --exclude 'node_modules' --exclude '.next' --exclude 'coverage' \
  ./mvs-vr-v2/ <EMAIL>:/opt/mvs-vr-v2/
```

### 3. Configure Environment

```bash
# SSH into droplet
ssh <EMAIL>
cd /opt/mvs-vr-v2

# Copy and edit environment file
cp .env.production .env
nano .env

# Generate secure passwords
openssl rand -hex 32  # For database passwords
openssl rand -hex 64  # For JWT secrets
```

### 4. Deploy Services

```bash
# Build and start all services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f app
```

## 🔧 Configuration

### Environment Variables

Key variables to configure in `.env`:

```bash
# Database
DB_PASSWORD=your-secure-password

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Directus
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=your-secure-password

# External APIs
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
```

### SSL/HTTPS Setup

1. **Get SSL Certificate** (Let's Encrypt recommended):

```bash
# Install certbot
apt install certbot

# Get certificate
certbot certonly --standalone -d your-domain.com

# Copy certificates
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/mvs-vr-v2/ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/mvs-vr-v2/ssl/key.pem
```

2. **Update Nginx Configuration**:
Uncomment the SSL server block in `deployment/nginx/default.conf`

3. **Restart Nginx**:

```bash
docker-compose restart nginx
```

## 📊 Monitoring & Access

### Service URLs

- **Main App**: `http://your-droplet-ip`
- **Admin Panel**: `http://your-droplet-ip/admin`
- **Monitoring**: `http://your-droplet-ip/monitoring`
- **Analytics**: `http://your-droplet-ip/analytics`
- **Object Storage**: `http://your-droplet-ip:9001`
- **Message Queue**: `http://your-droplet-ip:15672`

### Default Credentials

- **Directus**: `<EMAIL>` / `9elskdUeo@I!`
- **Grafana**: `mvsadmin` / `9elskdUeo@I!`
- **MinIO**: `mvsadmin` / `9elskdUeo@I!`
- **RabbitMQ**: `mvsadmin` / `9elskdUeo@I!`
- **Database**: `mvsadmin` / `9elskdUeo@I!`

⚠️ **These are secure default credentials, but consider changing them for your specific deployment!**

## 🔍 Troubleshooting

### Check Service Health

```bash
# Check all services
docker-compose ps

# Check specific service logs
docker-compose logs app
docker-compose logs directus
docker-compose logs postgres

# Check resource usage
docker stats
```

### Common Issues

1. **Out of Memory**:
   - Increase droplet size
   - Adjust resource limits in `docker-compose.prod.yml`

2. **Database Connection Issues**:
   - Check PostgreSQL logs: `docker-compose logs postgres`
   - Verify environment variables

3. **Nginx 502 Errors**:
   - Check if backend services are running
   - Verify upstream configuration

### Performance Optimization

1. **Database Tuning**:

```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U postgres -d mvs_vr

# Check performance
SELECT * FROM pg_stat_activity;
```

2. **Redis Monitoring**:

```bash
# Connect to Redis
docker-compose exec redis redis-cli

# Check memory usage
INFO memory
```

## 🔄 Updates & Maintenance

### Update Application

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose build app
docker-compose up -d app
```

### Backup Database

```bash
# Manual backup
docker-compose exec postgres pg_dump -U postgres mvs_vr > backup.sql

# Automated backups are configured in docker-compose.prod.yml
```

### Scale Services

```bash
# Scale application instances
docker-compose up -d --scale app=3
```

## 🛡️ Security Checklist

- [ ] Change all default passwords
- [ ] Configure SSL/HTTPS
- [ ] Set up firewall rules
- [ ] Enable fail2ban
- [ ] Configure log rotation
- [ ] Set up automated backups
- [ ] Monitor security logs

## 📞 Support

For deployment issues:

1. Check service logs
2. Verify environment configuration
3. Review resource usage
4. Check network connectivity

## 🔗 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [DigitalOcean Tutorials](https://www.digitalocean.com/community/tutorials)
- [Nginx Configuration](https://nginx.org/en/docs/)
- [PostgreSQL Tuning](https://wiki.postgresql.org/wiki/Tuning_Your_PostgreSQL_Server)
