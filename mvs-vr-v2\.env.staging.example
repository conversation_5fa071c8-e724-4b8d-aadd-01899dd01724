# MVS-VR Staging Environment Configuration
# Copy this file to .env.staging and update with actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=staging
PORT=3000
API_BASE_URL=https://api.mvs.kanousai.com
FRONTEND_URL=https://staging.mvs.kanousai.com
ADMIN_URL=https://admin.mvs.kanousai.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL
POSTGRES_PASSWORD=your_secure_postgres_password_here
DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/mvs_staging

# Redis
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your_secure_redis_password_here

# =============================================================================
# SUPABASE CONFIGURATION (Staging Instance)
# =============================================================================
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyODI3MjMsImV4cCI6MjA1OTg1ODcyM30.v7jSHJ4sPwnPzuwbYTCGxkwyDD4N8Ei89awcuXhPqhc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDI4MjcyNCwiZXhwIjoyMDU5ODU4NzI0fQ.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30

# =============================================================================
# DIRECTUS CMS CONFIGURATION
# =============================================================================
DIRECTUS_KEY=your_secure_directus_key_here_32_chars
DIRECTUS_SECRET=your_secure_directus_secret_here_64_chars
DIRECTUS_DB_PASSWORD=your_secure_directus_db_password_here
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=your_secure_admin_password_here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your_secure_jwt_secret_here_64_chars_minimum
CSRF_SECRET=your_secure_csrf_secret_here_32_chars
API_KEY=your_secure_api_key_here_32_chars
ENCRYPTION_KEY=your_secure_encryption_key_here_32_chars

# Session Configuration
SESSION_SECRET=your_secure_session_secret_here_64_chars
SESSION_TIMEOUT=3600000
COOKIE_SECURE=true
COOKIE_SAME_SITE=strict

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
EMAIL_FROM=<EMAIL>

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_FILES=10
LOG_MAX_SIZE=10m

# Sentry Error Tracking
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=staging

# Grafana
GRAFANA_USER=admin
GRAFANA_PASSWORD=your_secure_grafana_password_here

# =============================================================================
# FILE STORAGE
# =============================================================================
# DigitalOcean Spaces
DO_SPACES_ENDPOINT=https://nyc3.digitaloceanspaces.com
DO_SPACES_BUCKET=mvs-staging-assets
DO_SPACES_ACCESS_KEY=your_do_spaces_access_key
DO_SPACES_SECRET_KEY=your_do_spaces_secret_key

# Local Storage (fallback)
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,glb,gltf,fbx,obj

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================
# Unreal Engine Plugin
UE_PLUGIN_API_KEY=your_ue_plugin_api_key_here
UE_PLUGIN_WEBHOOK_SECRET=your_ue_webhook_secret_here

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your_mixpanel_token_here

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_WEBSOCKETS=true
ENABLE_REAL_TIME_SYNC=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_ML_FEATURES=false
ENABLE_BETA_FEATURES=false
ENABLE_DEBUG_MODE=false

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Database Connection Pool
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000

# Redis Configuration
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000
REDIS_CONNECT_TIMEOUT=10000

# Cache Settings
CACHE_TTL=3600
CACHE_MAX_SIZE=100MB
ENABLE_CACHE_COMPRESSION=true

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_CERT_PATH=/etc/letsencrypt/live/mvs.kanousai.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/mvs.kanousai.com/privkey.pem
SSL_CA_PATH=/etc/letsencrypt/live/mvs.kanousai.com/chain.pem

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=mvs-staging-backups
BACKUP_S3_ACCESS_KEY=your_backup_s3_access_key
BACKUP_S3_SECRET_KEY=your_backup_s3_secret_key

# =============================================================================
# DEVELOPMENT/TESTING
# =============================================================================
# Test Database
TEST_DATABASE_URL=postgresql://postgres:test_password@localhost:5433/mvs_test
TEST_REDIS_URL=redis://localhost:6380

# API Testing
TEST_API_BASE_URL=https://api.mvs.kanousai.com
TEST_TIMEOUT=30000
TEST_RETRIES=3

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGIN=https://staging.mvs.kanousai.com,https://admin.mvs.kanousai.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With,X-API-Key,X-CSRF-Token
CORS_CREDENTIALS=true

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================
WEBHOOK_SECRET=your_webhook_secret_here_32_chars
WEBHOOK_TIMEOUT=30000
WEBHOOK_RETRIES=3

# =============================================================================
# TIMEZONE & LOCALIZATION
# =============================================================================
TZ=America/New_York
DEFAULT_LOCALE=en-US
SUPPORTED_LOCALES=en-US,es-ES,fr-FR

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRIES=3

# =============================================================================
# DEPLOYMENT METADATA
# =============================================================================
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_DATE=2024-01-15
DEPLOYMENT_ENVIRONMENT=staging
GIT_COMMIT_HASH=placeholder_commit_hash
