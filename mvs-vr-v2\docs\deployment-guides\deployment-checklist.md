# DigitalOcean Deployment Checklist

## Pre-Deployment
- [ ] DigitalOcean account with billing enabled
- [ ] Domain name configured (mvs.kanousai.com)
- [ ] Local project files ready
- [ ] SSH key configured (recommended)

## Phase 1: Infrastructure Setup (15 minutes)
- [ ] **Create Volume**: `mvs-staging-data` (50GB, ext4)
- [ ] **Create Droplet**: Ubuntu 22.04, 4vCPU/8GB RAM, NYC1
- [ ] **Attach Volume**: Mount to droplet
- [ ] **Note IP Address**: Record public IP

## Phase 2: Server Setup (20 minutes)
- [ ] **SSH Connect**: `ssh root@YOUR_SERVER_IP`
- [ ] **Update System**: `apt update && apt upgrade -y`
- [ ] **Mount Volume**: `/mnt/mvs-data`
- [ ] **Install Docker**: Latest version + Docker Compose
- [ ] **Install Nginx**: `apt install nginx`
- [ ] **Configure Firewall**: UFW with ports 22, 80, 443

## Phase 3: File Upload (10 minutes)
- [ ] **Create Directory**: `/opt/mvs-vr`
- [ ] **Upload Files**: Git clone or SCP/rsync
- [ ] **Configure Environment**: Copy and edit `.env.staging`
- [ ] **Set Permissions**: `chmod +x scripts/deploy-staging.sh`
- [ ] **Create Volume Dirs**: `/mnt/mvs-data/docker-volumes/`

## Phase 4: Application Deploy (30 minutes)
- [ ] **Configure Volumes**: Create `docker-compose.override.yml`
- [ ] **Build Images**: `docker-compose build`
- [ ] **Start Services**: `docker-compose up -d`
- [ ] **Wait for Health**: Check all services ready
- [ ] **Verify Logs**: No critical errors

## Phase 5: Domain & SSL (20 minutes)
- [ ] **DNS Records**: A records for api, admin, staging
- [ ] **Configure Nginx**: Copy staging.conf
- [ ] **Install Certbot**: `apt install certbot python3-certbot-nginx`
- [ ] **Get SSL Certs**: For all 3 subdomains
- [ ] **Test Renewal**: `certbot renew --dry-run`

## Phase 6: Validation (15 minutes)
- [ ] **Health Checks**: All endpoints responding
- [ ] **Access Admin**: https://admin.mvs.kanousai.com
- [ ] **Test API**: https://api.mvs.kanousai.com/health
- [ ] **Check Monitoring**: Grafana dashboard
- [ ] **Verify SSL**: All certificates valid

## Phase 7: Backup Setup (10 minutes)
- [ ] **Backup Script**: Create automated backup
- [ ] **Cron Job**: Daily backups at 2 AM
- [ ] **Log Rotation**: Configure logrotate
- [ ] **Test Backup**: Run manual backup

## Critical Environment Variables
```bash
# Generate these securely
POSTGRES_PASSWORD=$(openssl rand -base64 32)
DIRECTUS_DB_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)
CSRF_SECRET=$(openssl rand -base64 32)
API_KEY=$(openssl rand -base64 32)
DIRECTUS_KEY=$(openssl rand -base64 32)
DIRECTUS_SECRET=$(openssl rand -base64 64)
DIRECTUS_ADMIN_PASSWORD=$(openssl rand -base64 16)
GRAFANA_PASSWORD=$(openssl rand -base64 16)

# Set these manually
DIRECTUS_ADMIN_EMAIL=<EMAIL>
```

## DNS Configuration
```
Type: A Record, Name: api, Value: YOUR_SERVER_IP
Type: A Record, Name: admin, Value: YOUR_SERVER_IP  
Type: A Record, Name: staging, Value: YOUR_SERVER_IP
```

## Service Ports
- **API**: 3000 (internal)
- **Directus**: 8055 (internal)
- **PostgreSQL**: 5432 (internal)
- **Redis**: 6379 (internal)
- **Nginx**: 80, 443 (external)
- **Grafana**: 3001 (internal)
- **Prometheus**: 9090 (internal)

## Health Check URLs
- **API**: https://api.mvs.kanousai.com/health
- **Directus**: https://admin.mvs.kanousai.com/server/health
- **Grafana**: http://YOUR_SERVER_IP:3001

## Common Issues & Solutions

### Volume Mount Issues
```bash
# Check if volume is attached
lsblk

# Remount if needed
umount /mnt/mvs-data
mount /dev/disk/by-id/scsi-0DO_Volume_mvs-staging-data /mnt/mvs-data
```

### Docker Permission Issues
```bash
# Add user to docker group
usermod -aG docker $USER
newgrp docker
```

### SSL Certificate Issues
```bash
# Check certificate status
certbot certificates

# Renew certificates
certbot renew --force-renewal
```

### Service Not Starting
```bash
# Check logs
docker-compose logs service_name

# Restart specific service
docker-compose restart service_name

# Rebuild and restart
docker-compose up -d --build service_name
```

## Rollback Procedure
```bash
# Stop services
docker-compose down

# Restore from backup
cd /mnt/mvs-data/backups/BACKUP_DATE
docker-compose exec -T postgres psql -U postgres -d mvs_staging < database.sql

# Restart services
docker-compose up -d
```

## Monitoring Commands
```bash
# Check all services
docker-compose ps

# View resource usage
docker stats

# Check disk space
df -h

# Monitor logs
docker-compose logs -f --tail=100
```

## Success Criteria
- [ ] All services showing as "Up" in `docker-compose ps`
- [ ] All health check URLs returning 200 OK
- [ ] SSL certificates valid for all domains
- [ ] Admin panel accessible with correct credentials
- [ ] Monitoring dashboard showing data
- [ ] Backup script working correctly

## Estimated Total Time: 2 hours

**Phase Breakdown:**
- Infrastructure: 15 min
- Server Setup: 20 min  
- File Upload: 10 min
- App Deploy: 30 min
- Domain/SSL: 20 min
- Validation: 15 min
- Backup Setup: 10 min

## Emergency Contacts
- **DigitalOcean Support**: Available 24/7
- **Domain Registrar**: Check support hours
- **SSL Issues**: Let's Encrypt community forums
